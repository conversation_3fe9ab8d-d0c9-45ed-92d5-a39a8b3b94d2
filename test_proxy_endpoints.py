#!/usr/bin/env python3

import requests
import json

# Test what endpoints the proxy supports
base_url = "https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
api_key = "sk-54870498"

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# Test basic chat completion (we know this works)
print("=== Testing chat completion ===")
chat_payload = {
    "model": "gemini-2.0-flash",
    "messages": [
        {"role": "user", "content": "Hello, this is a test"}
    ]
}

try:
    response = requests.post(
        f"{base_url}/v1/chat/completions",
        headers=headers,
        json=chat_payload,
        timeout=10
    )
    print(f"Chat completion status: {response.status_code}")
    if response.status_code == 200:
        print("✅ Chat completion works!")
    else:
        print(f"❌ Chat completion failed: {response.text}")
except Exception as e:
    print(f"❌ Chat completion error: {e}")

# Test models endpoint
print("\n=== Testing models endpoint ===")
try:
    response = requests.get(
        f"{base_url}/v1/models",
        headers=headers,
        timeout=10
    )
    print(f"Models endpoint status: {response.status_code}")
    if response.status_code == 200:
        print("✅ Models endpoint works!")
        models = response.json()
        print(f"Available models: {len(models.get('data', []))}")
        for model in models.get('data', [])[:5]:  # Show first 5 models
            print(f"  - {model.get('id', 'Unknown')}")
    else:
        print(f"❌ Models endpoint failed: {response.text}")
except Exception as e:
    print(f"❌ Models endpoint error: {e}")

print("\n=== Testing complete ===")
