import chromadb
from chromadb.config import Settings
from openai import OpenAI
from langchain_google_genai import GoogleGenerativeAIEmbeddings


class FinancialSituationMemory:
    def __init__(self, name, config):
        self.config = config
        if config["llm_provider"].lower() == "google":
            # Import the extension module to set custom API endpoint
            from langchain_google_genai import _genai_extension

            # Create a custom config with our API endpoint
            custom_config = _genai_extension.Config(
                api_endpoint=config["backend_url"]
            )
            _genai_extension.set_config(custom_config)

            self.embedding_client = GoogleGenerativeAIEmbeddings(
                model="models/embedding-001",
                google_api_key=config["GOOGLE_API_KEY"],
                transport="rest"  # 使用 REST 而不是 gRPC 來避免重定向問題
            )
        elif config["backend_url"] == "http://localhost:11434/v1":
            self.embedding = "nomic-embed-text"
            self.client = OpenAI(
                base_url=config["backend_url"],
                api_key=config.get("OPENAI_API_KEY", "dummy-key")
            )
        else:
            self.embedding = "text-embedding-3-small"
            self.client = OpenAI(
                base_url=config["backend_url"],
                api_key=config.get("OPENAI_API_KEY", "dummy-key")
            )
            
        self.chroma_client = chromadb.Client(Settings(allow_reset=True))
        self.situation_collection = self.chroma_client.create_collection(name=name)

    def get_embedding(self, text):
        """Get embedding for a text"""
        if self.config["llm_provider"].lower() == "google":
            try:
                return self.embedding_client.embed_query(text)
            except Exception as e:
                print(f"Warning: Embedding failed for Google provider: {e}")
                print("Falling back to simple text-based matching...")
                # Return a dummy embedding for now - we'll implement text-based fallback
                return [0.0] * 768  # Standard embedding dimension
        else:
            response = self.client.embeddings.create(
                model=self.embedding, input=text
            )
            return response.data[0].embedding

    def add_situations(self, situations_and_advice):
        """Add financial situations and their corresponding advice. Parameter is a list of tuples (situation, rec)"""
        try:
            situations = []
            advice = []
            ids = []
            embeddings = []

            offset = self.situation_collection.count()

            for i, (situation, recommendation) in enumerate(situations_and_advice):
                situations.append(situation)
                advice.append(recommendation)
                ids.append(str(offset + i))
                embeddings.append(self.get_embedding(situation))

            self.situation_collection.add(
                documents=situations,
                metadatas=[{"recommendation": rec} for rec in advice],
                embeddings=embeddings,
                ids=ids,
            )
        except Exception as e:
            print(f"Warning: Failed to add memories: {e}")
            print("Continuing without storing memories...")

    def get_memories(self, current_situation, n_matches=1):
        """Find matching recommendations using embeddings or fallback to empty results"""
        try:
            query_embedding = self.get_embedding(current_situation)

            # Check if we have any data in the collection
            if self.situation_collection.count() == 0:
                print("Warning: No memories stored in collection yet.")
                return []

            results = self.situation_collection.query(
                query_embeddings=[query_embedding],
                n_results=n_matches,
                include=["metadatas", "documents", "distances"],
            )

            matched_results = []
            for i in range(len(results["documents"][0])):
                matched_results.append(
                    {
                        "matched_situation": results["documents"][0][i],
                        "recommendation": results["metadatas"][0][i]["recommendation"],
                        "similarity_score": 1 - results["distances"][0][i],
                    }
                )

            return matched_results
        except Exception as e:
            print(f"Warning: Memory retrieval failed: {e}")
            print("Continuing without past memories...")
            return []


if __name__ == "__main__":
    # Example usage
    matcher = FinancialSituationMemory()

    # Example data
    example_data = [
        (
            "High inflation rate with rising interest rates and declining consumer spending",
            "Consider defensive sectors like consumer staples and utilities. Review fixed-income portfolio duration.",
        ),
        (
            "Tech sector showing high volatility with increasing institutional selling pressure",
            "Reduce exposure to high-growth tech stocks. Look for value opportunities in established tech companies with strong cash flows.",
        ),
        (
            "Strong dollar affecting emerging markets with increasing forex volatility",
            "Hedge currency exposure in international positions. Consider reducing allocation to emerging market debt.",
        ),
        (
            "Market showing signs of sector rotation with rising yields",
            "Rebalance portfolio to maintain target allocations. Consider increasing exposure to sectors benefiting from higher rates.",
        ),
    ]

    # Add the example situations and recommendations
    matcher.add_situations(example_data)

    # Example query
    current_situation = """
    Market showing increased volatility in tech sector, with institutional investors 
    reducing positions and rising interest rates affecting growth stock valuations
    """

    try:
        recommendations = matcher.get_memories(current_situation, n_matches=2)

        for i, rec in enumerate(recommendations, 1):
            print(f"\nMatch {i}:")
            print(f"Similarity Score: {rec['similarity_score']:.2f}")
            print(f"Matched Situation: {rec['matched_situation']}")
            print(f"Recommendation: {rec['recommendation']}")

    except Exception as e:
        print(f"Error during recommendation: {str(e)}")
