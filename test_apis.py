import os
from openai import OpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
import finnhub
import yfinance as yf
from binance.client import Client
from tradingagents.default_config import DEFAULT_CONFIG

def test_google_gemini():
    print("--- Testing Google Gemini API ---")
    import requests
    import json

    url = "https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com/gemini/v1beta/models/gemini-1.5-flash-latest:generateContent"
    
    headers = {
        'Content-Type': 'application/json',
        'X-goog-api-key': 'sk-54870498'
    }
    
    params = {}

    data = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "Explain how AI works in a few words"
                    }
                ]
            }
        ]
    }
    try:
        response = requests.post(url, headers=headers, params=params, data=json.dumps(data))
        response.raise_for_status()  # Will raise an exception for HTTP error codes
        print("Google Gemini API Test: SUCCESS")
        print("Response:", response.json())
    except Exception as e:
        print(f"Google Gemini API Test: FAILED - {e}")

def test_finnhub():
    print("\n--- Testing Finnhub API ---")
    try:
        finnhub_client = finnhub.Client(api_key=DEFAULT_CONFIG["FINNHUB_API_KEY"])
        quote = finnhub_client.quote('AAPL')
        print("Finnhub API Test: SUCCESS")
        print("AAPL Quote:", quote)
    except Exception as e:
        print(f"Finnhub API Test: FAILED - {e}")

def test_binance():
    print("\n--- Testing Binance API ---")
    try:
        client = Client()
        ticker = client.get_ticker(symbol='BTCUSDT')
        print("Binance API Test: SUCCESS")
        print("BTCUSDT Ticker:", ticker)
    except Exception as e:
        print(f"Binance API Test: FAILED - {e}")

def test_yfinance():
    print("\n--- Testing yfinance ---")
    try:
        ticker = yf.Ticker("AAPL")
        hist = ticker.history(period="1d")
        print("yfinance Test: SUCCESS")
        print("AAPL History:", hist.head())
    except Exception as e:
        print(f"yfinance Test: FAILED - {e}")

if __name__ == "__main__":
    test_google_gemini()
    test_finnhub()
    test_binance()
    test_yfinance()