# Gemini API 代理配置修復

## 問題描述

在使用代理的 Gemini API 時遇到以下問題：
- 使用代理密鑰：`sk-54870498`
- 使用代理 URL：`https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com`
- 但調用時一直失敗，似乎 URL 被導向到原來預設的 Google API URL

## 根本原因

問題出現在 `langchain_google_genai` 庫的實現中：

1. **全局配置覆蓋**：庫使用全局的 `_config.api_endpoint` 作為默認值，即使在 `client_options` 中指定了自定義端點，也會被全局配置覆蓋。

2. **gRPC 重定向問題**：代理服務器返回 HTTP 307 重定向，但 gRPC 客戶端無法正確處理重定向。

## 解決方案

### 1. 修改 TradingAgentsGraph 初始化

在 `tradingagents/graph/trading_graph.py` 中：

```python
elif self.config["llm_provider"].lower() == "google":
    # Import the extension module to set custom API endpoint
    from langchain_google_genai import _genai_extension
    
    # Create a custom config with our API endpoint
    custom_config = _genai_extension.Config(
        api_endpoint=self.config["backend_url"]
    )
    _genai_extension.set_config(custom_config)
    
    self.deep_thinking_llm = ChatGoogleGenerativeAI(
        model=self.config["deep_think_llm"],
        google_api_key=self.config["GOOGLE_API_KEY"],
        transport="rest",  # 使用 REST 而不是 gRPC 來避免重定向問題
    )
    self.quick_thinking_llm = ChatGoogleGenerativeAI(
        model=self.config["quick_think_llm"],
        google_api_key=self.config["GOOGLE_API_KEY"],
        transport="rest",  # 使用 REST 而不是 gRPC 來避免重定向問題
    )
```

### 2. 修改記憶體模組

在 `tradingagents/agents/utils/memory.py` 中：

```python
if config["llm_provider"].lower() == "google":
    # Import the extension module to set custom API endpoint
    from langchain_google_genai import _genai_extension
    
    # Create a custom config with our API endpoint
    custom_config = _genai_extension.Config(
        api_endpoint=config["backend_url"]
    )
    _genai_extension.set_config(custom_config)
    
    self.embedding_client = GoogleGenerativeAIEmbeddings(
        model="models/embedding-001", 
        google_api_key=config["GOOGLE_API_KEY"],
        transport="rest"  # 使用 REST 而不是 gRPC 來避免重定向問題
    )
```

### 3. 更新默認配置

在 `tradingagents/default_config.py` 中：

```python
"backend_url": "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com",  # 移除 https:// 前綴
```

## 關鍵修復點

1. **設置全局 API 端點**：使用 `_genai_extension.set_config()` 設置全局 API 端點
2. **使用 REST 傳輸**：指定 `transport="rest"` 來避免 gRPC 重定向問題
3. **正確的 URL 格式**：移除 URL 中的 `https://` 前綴，因為庫會自動添加

## 測試結果

修復後的測試結果：

```
=== 測試總結 ===
直接 API 調用: ✅
LangChain 調用: ✅

=== TradingAgentsGraph 測試 ===
初始化測試: ✅
Quick thinking LLM: ✅
Deep thinking LLM: ✅
```

## 使用方法

現在您可以正常使用代理的 Gemini API：

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# 使用默認配置（已包含代理設置）
graph = TradingAgentsGraph(debug=True, config=DEFAULT_CONFIG)

# 或者使用自定義配置
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "google"
config["backend_url"] = "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
config["GOOGLE_API_KEY"] = "sk-54870498"

graph = TradingAgentsGraph(debug=True, config=config)
```

## 注意事項

1. **API 密鑰**：確保使用正確的代理密鑰 `sk-54870498`
2. **URL 格式**：不要在 `backend_url` 中包含 `https://` 前綴
3. **傳輸方式**：必須使用 `transport="rest"` 來避免 gRPC 重定向問題
4. **全局配置**：修復會設置全局的 API 端點配置，影響所有後續的 Google AI 客戶端

修復完成！現在您可以成功使用代理的 Gemini API 進行交易分析了。
