#!/usr/bin/env python3
"""
測試修復後的 Gemini API 配置
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

from langchain_google_genai import ChatGoogleGenerativeAI, _genai_extension
from tradingagents.default_config import DEFAULT_CONFIG

def test_direct_gemini_api():
    """直接測試 Gemini API 調用"""
    print("=== 測試直接 Gemini API 調用 ===")
    
    try:
        # 設置自定義 API 端點
        custom_config = _genai_extension.Config(
            api_endpoint=DEFAULT_CONFIG["backend_url"]
        )
        _genai_extension.set_config(custom_config)
        
        # 創建 ChatGoogleGenerativeAI 實例
        llm = ChatGoogleGenerativeAI(
            model=DEFAULT_CONFIG["quick_think_llm"],
            google_api_key=DEFAULT_CONFIG["GOOGLE_API_KEY"],
        )
        
        # 測試簡單的調用
        response = llm.invoke("請用一句話解釋什麼是人工智能")
        print(f"✅ 成功！回應: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        return False

def test_trading_agents_graph():
    """測試 TradingAgentsGraph 的初始化"""
    print("\n=== 測試 TradingAgentsGraph 初始化 ===")
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        
        # 創建自定義配置
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "google"
        config["backend_url"] = "https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
        config["GOOGLE_API_KEY"] = "sk-54870498"
        
        # 初始化 TradingAgentsGraph
        graph = TradingAgentsGraph(
            selected_analysts=["market"],
            debug=True,
            config=config
        )
        
        print("✅ TradingAgentsGraph 初始化成功！")
        
        # 測試 LLM 調用
        response = graph.quick_thinking_llm.invoke("測試消息")
        print(f"✅ LLM 調用成功！回應: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint_configuration():
    """測試 API 端點配置"""
    print("\n=== 測試 API 端點配置 ===")
    
    try:
        # 檢查當前配置
        current_config = _genai_extension.get_config()
        print(f"當前 API 端點: {current_config.api_endpoint}")
        
        # 設置自定義端點
        custom_config = _genai_extension.Config(
            api_endpoint="https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
        )
        _genai_extension.set_config(custom_config)
        
        # 驗證配置已更新
        updated_config = _genai_extension.get_config()
        print(f"更新後 API 端點: {updated_config.api_endpoint}")
        
        if updated_config.api_endpoint == "https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com":
            print("✅ API 端點配置成功！")
            return True
        else:
            print("❌ API 端點配置失敗！")
            return False
            
    except Exception as e:
        print(f"❌ 失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始測試 Gemini API 修復...")
    
    # 運行所有測試
    tests = [
        test_api_endpoint_configuration,
        test_direct_gemini_api,
        test_trading_agents_graph,
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    # 總結結果
    print(f"\n=== 測試總結 ===")
    print(f"通過: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有測試都通過了！Gemini API 配置修復成功！")
    else:
        print("⚠️  部分測試失敗，需要進一步調試。")
