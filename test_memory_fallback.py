#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.agents.utils.memory import FinancialSituationMemory

# Test the memory system with Google provider (which will fail embedding)
config = {
    "llm_provider": "google",
    "backend_url": "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com",
    "GOOGLE_API_KEY": "sk-54870498"
}

print("=== Testing Memory System with Fallback ===")

try:
    # Initialize memory
    memory = FinancialSituationMemory("test_memory", config)
    print("✅ Memory system initialized successfully")
    
    # Test get_memories with empty collection
    print("\n--- Testing get_memories with empty collection ---")
    memories = memory.get_memories("Test situation", n_matches=2)
    print(f"Memories returned: {len(memories)}")
    
    # Test add_situations (this will fail embedding but should handle gracefully)
    print("\n--- Testing add_situations ---")
    test_situations = [
        ("Market is volatile", "Consider reducing position size"),
        ("Strong uptrend", "Consider increasing position")
    ]
    memory.add_situations(test_situations)
    print("✅ add_situations completed (with or without embedding)")
    
    # Test get_memories again
    print("\n--- Testing get_memories after adding situations ---")
    memories = memory.get_memories("Market conditions are uncertain", n_matches=2)
    print(f"Memories returned: {len(memories)}")
    
    print("\n✅ All memory tests completed successfully!")
    
except Exception as e:
    print(f"❌ Memory test failed: {e}")
    import traceback
    traceback.print_exc()
