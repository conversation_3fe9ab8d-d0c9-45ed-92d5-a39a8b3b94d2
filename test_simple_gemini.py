#!/usr/bin/env python3
"""
簡單測試 Gemini API 配置
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

def test_simple_api_call():
    """測試簡單的 API 調用"""
    print("=== 測試簡單 API 調用 ===")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI, _genai_extension
        from tradingagents.default_config import DEFAULT_CONFIG
        
        print(f"默認配置 backend_url: {DEFAULT_CONFIG['backend_url']}")
        print(f"默認配置 GOOGLE_API_KEY: {DEFAULT_CONFIG['GOOGLE_API_KEY']}")
        
        # 設置自定義 API 端點
        custom_config = _genai_extension.Config(
            api_endpoint=DEFAULT_CONFIG["backend_url"]
        )
        _genai_extension.set_config(custom_config)
        
        # 檢查配置
        current_config = _genai_extension.get_config()
        print(f"設置後的 API 端點: {current_config.api_endpoint}")
        
        # 創建 ChatGoogleGenerativeAI 實例
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash-latest",
            google_api_key=DEFAULT_CONFIG["GOOGLE_API_KEY"],
            timeout=30,  # 設置超時
            transport="rest",  # 使用 REST 而不是 gRPC
        )
        
        print("ChatGoogleGenerativeAI 實例創建成功")
        
        # 測試簡單的調用
        print("開始 API 調用...")
        response = llm.invoke("Hello")
        print(f"✅ 成功！回應: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_requests_direct():
    """使用 requests 直接測試 API"""
    print("\n=== 使用 requests 直接測試 ===")
    
    try:
        import requests
        import json
        from tradingagents.default_config import DEFAULT_CONFIG
        
        url = f"https://{DEFAULT_CONFIG['backend_url']}/v1beta/models/gemini-1.5-flash-latest:generateContent"
        
        headers = {
            'Content-Type': 'application/json',
            'x-goog-api-key': DEFAULT_CONFIG['GOOGLE_API_KEY']
        }
        
        data = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": "Hello"
                        }
                    ]
                }
            ]
        }
        
        print(f"請求 URL: {url}")
        print(f"請求頭: {headers}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功！回應: {result}")
            return True
        else:
            print(f"❌ 失敗！響應: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始簡單測試...")
    
    # 先測試直接 requests 調用
    requests_success = test_requests_direct()
    
    if requests_success:
        # 如果 requests 成功，再測試 langchain
        langchain_success = test_simple_api_call()
    else:
        print("跳過 langchain 測試，因為直接 API 調用失敗")
        langchain_success = False
    
    print(f"\n=== 測試總結 ===")
    print(f"直接 API 調用: {'✅' if requests_success else '❌'}")
    print(f"LangChain 調用: {'✅' if langchain_success else '❌'}")
