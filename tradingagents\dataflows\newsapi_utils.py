import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import time
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)


class NewsAPIError(Exception):
    """Custom exception for NewsAPI errors"""
    pass


class NewsAPIClient:
    """
    NewsAPI client for fetching news articles
    
    Requires NewsAPI key from https://newsapi.org/
    """
    
    def __init__(self, api_key: str):
        """
        Initialize NewsAPI client
        
        Args:
            api_key (str): NewsAPI key from https://newsapi.org/
        """
        self.api_key = api_key
        self.base_url = "https://newsapi.org/v2"
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': self.api_key,
            'User-Agent': 'TradingAgents/1.0'
        })
    
    @retry(
        retry=retry_if_exception_type((requests.RequestException, NewsAPIError)),
        wait=wait_exponential(multiplier=1, min=4, max=60),
        stop=stop_after_attempt(3),
    )
    def _make_request(self, endpoint: str, params: Dict) -> Dict:
        """
        Make API request with retry logic
        
        Args:
            endpoint (str): API endpoint
            params (Dict): Request parameters
            
        Returns:
            Dict: API response data
            
        Raises:
            NewsAPIError: If API returns error
        """
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'ok':
                error_msg = data.get('message', 'Unknown API error')
                raise NewsAPIError(f"NewsAPI error: {error_msg}")
            
            return data
            
        except requests.RequestException as e:
            raise NewsAPIError(f"Request failed: {str(e)}")
    
    def search_everything(
        self,
        query: str,
        start_date: str,
        end_date: str,
        language: str = 'en',
        sort_by: str = 'publishedAt',
        page_size: int = 100,
        max_pages: int = 5
    ) -> List[Dict]:
        """
        Search for news articles using the everything endpoint
        
        Args:
            query (str): Search query
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
            language (str): Language code (default: 'en')
            sort_by (str): Sort order ('relevancy', 'popularity', 'publishedAt')
            page_size (int): Number of articles per page (max 100)
            max_pages (int): Maximum number of pages to fetch
            
        Returns:
            List[Dict]: List of news articles
        """
        articles = []
        page = 1
        
        # Validate date format
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            raise NewsAPIError("Date format must be YYYY-MM-DD")
        
        while page <= max_pages:
            params = {
                'q': query,
                'from': start_date,
                'to': end_date,
                'language': language,
                'sortBy': sort_by,
                'pageSize': page_size,
                'page': page
            }
            
            try:
                data = self._make_request('everything', params)
                
                page_articles = data.get('articles', [])
                if not page_articles:
                    break
                
                articles.extend(page_articles)
                
                # Check if we've reached the total available articles
                total_results = data.get('totalResults', 0)
                if len(articles) >= total_results:
                    break
                
                page += 1
                
                # Rate limiting - NewsAPI allows 1000 requests per day for free tier
                time.sleep(0.1)
                
            except NewsAPIError as e:
                print(f"Error fetching page {page}: {e}")
                break
        
        return articles
    
    def get_top_headlines(
        self,
        query: Optional[str] = None,
        category: Optional[str] = None,
        country: str = 'us',
        page_size: int = 100
    ) -> List[Dict]:
        """
        Get top headlines
        
        Args:
            query (str, optional): Search query
            category (str, optional): Category ('business', 'entertainment', 'general', 
                                    'health', 'science', 'sports', 'technology')
            country (str): Country code (default: 'us')
            page_size (int): Number of articles per page (max 100)
            
        Returns:
            List[Dict]: List of news articles
        """
        params = {
            'country': country,
            'pageSize': page_size
        }
        
        if query:
            params['q'] = query
        if category:
            params['category'] = category
        
        try:
            data = self._make_request('top-headlines', params)
            return data.get('articles', [])
        except NewsAPIError as e:
            print(f"Error fetching top headlines: {e}")
            return []


def get_news_data_newsapi(
    query: str,
    start_date: str,
    end_date: str,
    api_key: str,
    language: str = 'en',
    sort_by: str = 'publishedAt',
    max_articles: int = 500
) -> List[Dict]:
    """
    Fetch news data using NewsAPI
    
    Args:
        query (str): Search query
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
        api_key (str): NewsAPI key
        language (str): Language code (default: 'en')
        sort_by (str): Sort order ('relevancy', 'popularity', 'publishedAt')
        max_articles (int): Maximum number of articles to fetch
        
    Returns:
        List[Dict]: List of news articles with standardized format
    """
    client = NewsAPIClient(api_key)
    
    try:
        # Calculate optimal page size and max pages
        page_size = min(100, max_articles)  # NewsAPI max is 100 per page
        max_pages = (max_articles + page_size - 1) // page_size
        
        articles = client.search_everything(
            query=query,
            start_date=start_date,
            end_date=end_date,
            language=language,
            sort_by=sort_by,
            page_size=page_size,
            max_pages=max_pages
        )
        
        # Standardize the format to match existing interface
        standardized_articles = []
        for article in articles[:max_articles]:
            standardized_article = {
                'title': article.get('title', ''),
                'snippet': article.get('description', ''),
                'link': article.get('url', ''),
                'source': article.get('source', {}).get('name', ''),
                'date': article.get('publishedAt', ''),
                'author': article.get('author', ''),
                'content': article.get('content', '')
            }
            standardized_articles.append(standardized_article)
        
        return standardized_articles
        
    except Exception as e:
        print(f"Error fetching news data: {e}")
        return []


def format_news_for_analysis(articles: List[Dict], query: str, start_date: str, end_date: str) -> str:
    """
    Format news articles for analysis
    
    Args:
        articles (List[Dict]): List of news articles
        query (str): Search query used
        start_date (str): Start date
        end_date (str): End date
        
    Returns:
        str: Formatted news string for analysis
    """
    if not articles:
        return f"## {query} News from {start_date} to {end_date}:\n\nNo news articles found for the specified period."
    
    news_str = ""
    for i, article in enumerate(articles, 1):
        title = article.get('title', 'No title')
        snippet = article.get('snippet', 'No description')
        source = article.get('source', 'Unknown source')
        date = article.get('date', 'Unknown date')
        link = article.get('link', '')
        
        # Format date for better readability
        try:
            if 'T' in date:  # ISO format from NewsAPI
                formatted_date = datetime.fromisoformat(date.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
            else:
                formatted_date = date
        except:
            formatted_date = date
        
        news_str += f"### {i}. {title}\n"
        news_str += f"**Source:** {source} | **Date:** {formatted_date}\n"
        news_str += f"**Description:** {snippet}\n"
        if link:
            news_str += f"**Link:** {link}\n"
        news_str += "\n"
    
    header = f"## {query} News from {start_date} to {end_date} (Total: {len(articles)} articles):\n\n"
    return header + news_str
