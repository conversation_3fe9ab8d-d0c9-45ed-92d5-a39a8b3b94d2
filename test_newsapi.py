#!/usr/bin/env python3
"""
Test script for NewsAPI integration in TradingAgents
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from tradingagents.dataflows.newsapi_utils import get_news_data_newsapi, format_news_for_analysis
from tradingagents.dataflows.interface import get_newsapi_news


def test_newsapi_basic():
    """Test basic NewsAPI functionality"""
    print("=== Testing Basic NewsAPI Functionality ===")
    
    # Test parameters
    api_key = input("請輸入您的 NewsAPI 密鑰 (從 https://newsapi.org/ 獲取): ").strip()
    
    if not api_key:
        print("❌ 需要 NewsAPI 密鑰才能進行測試")
        return False
    
    query = "Bitcoin"
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    
    print(f"查詢: {query}")
    print(f"日期範圍: {start_date} 到 {end_date}")
    print()
    
    try:
        # Test the basic NewsAPI function
        articles = get_news_data_newsapi(
            query=query,
            start_date=start_date,
            end_date=end_date,
            api_key=api_key,
            max_articles=5
        )
        
        if articles:
            print(f"✅ 成功獲取 {len(articles)} 篇文章")
            
            # Display first article details
            first_article = articles[0]
            print("\n第一篇文章詳情:")
            print(f"標題: {first_article.get('title', 'N/A')}")
            print(f"來源: {first_article.get('source', 'N/A')}")
            print(f"日期: {first_article.get('date', 'N/A')}")
            print(f"描述: {first_article.get('snippet', 'N/A')[:100]}...")
            
            # Test formatting
            formatted = format_news_for_analysis(articles, query, start_date, end_date)
            print(f"\n✅ 格式化成功，總長度: {len(formatted)} 字符")
            
            return True
        else:
            print("❌ 沒有獲取到文章")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        return False


def test_newsapi_interface():
    """Test NewsAPI interface function"""
    print("\n=== Testing NewsAPI Interface Function ===")
    
    api_key = input("請輸入您的 NewsAPI 密鑰: ").strip()
    
    if not api_key:
        print("❌ 需要 NewsAPI 密鑰才能進行測試")
        return False
    
    try:
        # Test the interface function
        result = get_newsapi_news(
            query="Apple stock",
            curr_date=datetime.now().strftime("%Y-%m-%d"),
            look_back_days=3,
            api_key=api_key,
            max_articles=3
        )
        
        if "Error" not in result:
            print("✅ Interface 函數測試成功")
            print(f"結果長度: {len(result)} 字符")
            print("\n前 200 字符預覽:")
            print(result[:200] + "...")
            return True
        else:
            print(f"❌ Interface 函數返回錯誤: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Interface 函數測試失敗: {str(e)}")
        return False


def test_newsapi_different_queries():
    """Test NewsAPI with different query types"""
    print("\n=== Testing Different Query Types ===")
    
    api_key = input("請輸入您的 NewsAPI 密鑰: ").strip()
    
    if not api_key:
        print("❌ 需要 NewsAPI 密鑰才能進行測試")
        return False
    
    queries = [
        "cryptocurrency",
        "Federal Reserve",
        "stock market",
        "inflation"
    ]
    
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")
    
    success_count = 0
    
    for query in queries:
        print(f"\n測試查詢: {query}")
        try:
            articles = get_news_data_newsapi(
                query=query,
                start_date=start_date,
                end_date=end_date,
                api_key=api_key,
                max_articles=2
            )
            
            if articles:
                print(f"✅ 成功獲取 {len(articles)} 篇文章")
                success_count += 1
            else:
                print("⚠️ 沒有找到相關文章")
                
        except Exception as e:
            print(f"❌ 查詢失敗: {str(e)}")
    
    print(f"\n總結: {success_count}/{len(queries)} 個查詢成功")
    return success_count > 0


def main():
    """Main test function"""
    print("TradingAgents NewsAPI 集成測試")
    print("=" * 50)
    
    # Check if requests is available
    try:
        import requests
        print("✅ requests 庫可用")
    except ImportError:
        print("❌ 需要安裝 requests 庫: pip install requests")
        return
    
    # Run tests
    test_results = []
    
    test_results.append(test_newsapi_basic())
    test_results.append(test_newsapi_interface())
    test_results.append(test_newsapi_different_queries())
    
    # Summary
    print("\n" + "=" * 50)
    print("測試總結:")
    passed = sum(test_results)
    total = len(test_results)
    print(f"通過: {passed}/{total} 個測試")
    
    if passed == total:
        print("🎉 所有測試通過！NewsAPI 集成成功")
    else:
        print("⚠️ 部分測試失敗，請檢查配置和網絡連接")
    
    print("\n使用說明:")
    print("1. 獲取 NewsAPI 密鑰: https://newsapi.org/")
    print("2. 在 tradingagents/default_config.py 中設置 NEWSAPI_KEY")
    print("3. 在代理中使用 get_newsapi_news 工具獲取新聞")


if __name__ == "__main__":
    main()
