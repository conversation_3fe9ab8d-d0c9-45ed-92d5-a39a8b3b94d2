import os

DEFAULT_CONFIG = {
    "project_dir": os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
    "results_dir": os.getenv("TRADINGAGENTS_RESULTS_DIR", "./results"),
    "data_dir": "/Users/<USER>/Documents/Code/ScAI/FR1-data",
    "data_cache_dir": os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
        "dataflows/data_cache",
    ),
    # LLM settings
    "llm_provider": "google",
    "deep_think_llm": "gemini-2.5-pro-latest",
    "quick_think_llm": "gemini-2.5-flash-latest",
    "backend_url": "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com",
    # Debate and discussion settings
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    "max_recur_limit": 100,
    # Tool settings
    "online_tools": True,
    "FINNHUB_API_KEY": "d1lv1d9r01qksvur9q2gd1lv1d9r01qksvur9q30",
    "GOOGLE_API_KEY": "sk-54870498",
    "NEWSAPI_KEY": "********************************",  # NewsAPI key from https://newsapi.org/
}
