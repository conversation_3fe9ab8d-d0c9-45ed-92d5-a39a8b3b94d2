# NewsAPI 集成指南

## 概述

TradingAgents 現在支持使用 NewsAPI 來獲取新聞數據，這提供了比 Google News 爬蟲更穩定和可靠的新聞來源。

## 功能特點

- **穩定的 API 接口**: 使用官方 NewsAPI，避免網頁爬蟲的不穩定性
- **豐富的新聞來源**: 支持來自全球 80,000+ 新聞來源的文章
- **靈活的查詢選項**: 支持關鍵詞搜索、日期範圍、語言和排序選項
- **速率限制處理**: 內建重試機制和速率限制處理
- **標準化輸出**: 與現有系統兼容的格式化輸出

## 配置設置

### 1. 獲取 NewsAPI 密鑰

1. 訪問 [NewsAPI 官網](https://newsapi.org/)
2. 註冊免費帳戶
3. 獲取您的 API 密鑰

### 2. 配置密鑰

在 `tradingagents/default_config.py` 中設置您的 NewsAPI 密鑰：

```python
DEFAULT_CONFIG = {
    # ... 其他配置 ...
    "NEWSAPI_KEY": "your_newsapi_key_here",
}
```

## 使用方法

### 1. 在代理中使用

NewsAPI 工具已經集成到 `Toolkit` 類中，可以在代理中直接使用：

```python
# 在代理工具列表中包含 NewsAPI 工具
tools = [
    Toolkit.get_newsapi_news,
    # ... 其他工具 ...
]
```

### 2. 直接調用接口

您也可以直接調用接口函數：

```python
from tradingagents.dataflows.interface import get_newsapi_news

result = get_newsapi_news(
    query="Bitcoin",
    curr_date="2024-01-15",
    look_back_days=7,
    api_key="your_api_key",
    language="en",
    sort_by="publishedAt",
    max_articles=50
)
```

### 3. 參數說明

- **query** (str): 搜索查詢關鍵詞
- **curr_date** (str): 當前日期，格式為 YYYY-MM-DD
- **look_back_days** (int): 回溯天數，默認為 7 天
- **api_key** (str): NewsAPI 密鑰
- **language** (str): 語言代碼，默認為 'en'
- **sort_by** (str): 排序方式
  - `'publishedAt'`: 按發布時間排序（默認）
  - `'relevancy'`: 按相關性排序
  - `'popularity'`: 按熱門程度排序
- **max_articles** (int): 最大文章數量，默認為 100

## API 限制

### 免費版限制
- 每月 1,000 次請求
- 每次請求最多 100 篇文章
- 只能獲取過去 30 天的文章
- 不包含文章全文

### 付費版功能
- 更高的請求限制
- 可獲取歷史文章（最多 2 年）
- 包含文章全文
- 更多高級功能

## 錯誤處理

系統包含完整的錯誤處理機制：

1. **API 密鑰驗證**: 自動檢查密鑰是否配置
2. **網絡錯誤重試**: 使用 tenacity 庫進行自動重試
3. **速率限制處理**: 自動處理 API 速率限制
4. **數據驗證**: 驗證日期格式和參數有效性

## 測試

運行測試腳本來驗證 NewsAPI 集成：

```bash
python test_newsapi.py
```

測試腳本將驗證：
- 基本 NewsAPI 功能
- 接口函數集成
- 不同查詢類型的處理

## 與現有系統的比較

| 特性 | Google News 爬蟲 | NewsAPI |
|------|------------------|---------|
| 穩定性 | 中等（依賴網頁結構） | 高（官方 API） |
| 速度 | 較慢（需要解析 HTML） | 快（JSON 響應） |
| 可靠性 | 可能被反爬蟲機制阻擋 | 高（官方支持） |
| 數據質量 | 依賴爬蟲解析準確性 | 高（結構化數據） |
| 成本 | 免費 | 免費版有限制 |

## 最佳實踐

1. **合理使用配額**: 監控 API 使用量，避免超出限制
2. **緩存結果**: 對於相同查詢，考慮緩存結果以節省 API 調用
3. **錯誤處理**: 始終處理 API 錯誤和網絡問題
4. **查詢優化**: 使用具體的關鍵詞以獲得更相關的結果

## 故障排除

### 常見問題

1. **API 密鑰錯誤**
   - 檢查密鑰是否正確配置
   - 確認密鑰在 NewsAPI 控制台中有效

2. **沒有返回結果**
   - 檢查查詢關鍵詞是否過於具體
   - 確認日期範圍內有相關新聞
   - 嘗試不同的語言設置

3. **速率限制錯誤**
   - 檢查是否超出每月配額
   - 考慮升級到付費計劃

4. **網絡連接問題**
   - 檢查網絡連接
   - 確認防火牆設置允許 HTTPS 請求

## 未來改進

計劃中的功能增強：
- 新聞情感分析集成
- 自動關鍵詞提取
- 新聞分類和標籤
- 實時新聞流處理
- 多語言新聞支持優化
