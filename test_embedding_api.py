#!/usr/bin/env python3

import requests
import json

# Test different embedding endpoints
base_url = "https://iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
api_key = "sk-54870498"

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# Test data
test_text = "This is a test for embedding"

# Test different endpoint variations
endpoints_to_test = [
    "/v1/embeddings",  # OpenAI-style endpoint
    "/v1beta/models/embedding-001:embedContent",  # Google v1beta endpoint
    "/v1/models/embedding-001:embedContent",  # Google v1 endpoint
    "/v1beta/models/text-embedding-004:embedContent",  # Different model
    "/v1/models/text-embedding-004:embedContent",  # Different model v1
]

for endpoint in endpoints_to_test:
    print(f"\n=== Testing endpoint: {endpoint} ===")
    
    if "embedContent" in endpoint:
        # Google-style request
        payload = {
            "model": "models/embedding-001",
            "content": {
                "parts": [{"text": test_text}]
            },
            "taskType": "RETRIEVAL_QUERY"
        }
    else:
        # OpenAI-style request
        payload = {
            "model": "text-embedding-3-small",
            "input": test_text
        }
    
    try:
        response = requests.post(
            f"{base_url}{endpoint}",
            headers=headers,
            json=payload,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✅ SUCCESS!")
            result = response.json()
            if "embedding" in result:
                print(f"Embedding length: {len(result['embedding']['values'])}")
            elif "data" in result:
                print(f"Embedding length: {len(result['data'][0]['embedding'])}")
        else:
            print(f"❌ FAILED: {response.text}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

print("\n=== Testing complete ===")
