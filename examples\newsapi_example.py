#!/usr/bin/env python3
"""
NewsAPI 使用示例
演示如何在 TradingAgents 中使用 NewsAPI 獲取和分析新聞
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from tradingagents.agents.utils.agent_utils import Toolkit
from tradingagents.dataflows.interface import get_newsapi_news
from tradingagents.default_config import DEFAULT_CONFIG


def example_basic_newsapi_usage():
    """基本 NewsAPI 使用示例"""
    print("=== 基本 NewsAPI 使用示例 ===")
    
    # 設置參數
    query = "Bitcoin price"
    curr_date = datetime.now().strftime("%Y-%m-%d")
    look_back_days = 3
    
    print(f"查詢: {query}")
    print(f"日期: {curr_date}")
    print(f"回溯天數: {look_back_days}")
    print()
    
    try:
        # 直接調用 NewsAPI 接口
        result = get_newsapi_news(
            query=query,
            curr_date=curr_date,
            look_back_days=look_back_days,
            api_key=DEFAULT_CONFIG["NEWSAPI_KEY"],
            max_articles=5
        )
        
        print("新聞結果:")
        print(result)
        print("\n" + "="*50 + "\n")
        
    except Exception as e:
        print(f"錯誤: {str(e)}")


def example_toolkit_usage():
    """使用 Toolkit 類的示例"""
    print("=== Toolkit 類使用示例 ===")
    
    # 創建 Toolkit 實例
    toolkit = Toolkit()
    
    # 檢查 NewsAPI 密鑰是否配置
    if not DEFAULT_CONFIG.get("NEWSAPI_KEY"):
        print("❌ NewsAPI 密鑰未配置，請在 default_config.py 中設置 NEWSAPI_KEY")
        return
    
    try:
        # 使用 Toolkit 的 NewsAPI 工具
        result = toolkit.get_newsapi_news.invoke({
            "query": "Federal Reserve interest rates",
            "curr_date": datetime.now().strftime("%Y-%m-%d"),
            "look_back_days": 5,
            "max_articles": 3
        })
        
        print("聯準會利率相關新聞:")
        print(result)
        print("\n" + "="*50 + "\n")
        
    except Exception as e:
        print(f"錯誤: {str(e)}")


def example_multiple_queries():
    """多個查詢示例"""
    print("=== 多個查詢示例 ===")
    
    queries = [
        "Apple earnings",
        "Tesla stock",
        "cryptocurrency regulation",
        "inflation data"
    ]
    
    curr_date = datetime.now().strftime("%Y-%m-%d")
    
    for query in queries:
        print(f"\n--- 查詢: {query} ---")
        
        try:
            result = get_newsapi_news(
                query=query,
                curr_date=curr_date,
                look_back_days=2,
                api_key=DEFAULT_CONFIG["NEWSAPI_KEY"],
                max_articles=2
            )
            
            # 只顯示前 200 字符
            preview = result[:200] + "..." if len(result) > 200 else result
            print(preview)
            
        except Exception as e:
            print(f"錯誤: {str(e)}")
    
    print("\n" + "="*50 + "\n")


def example_different_sort_options():
    """不同排序選項示例"""
    print("=== 不同排序選項示例 ===")
    
    query = "stock market"
    curr_date = datetime.now().strftime("%Y-%m-%d")
    sort_options = ['publishedAt', 'relevancy', 'popularity']
    
    for sort_by in sort_options:
        print(f"\n--- 排序方式: {sort_by} ---")
        
        try:
            result = get_newsapi_news(
                query=query,
                curr_date=curr_date,
                look_back_days=1,
                api_key=DEFAULT_CONFIG["NEWSAPI_KEY"],
                sort_by=sort_by,
                max_articles=2
            )
            
            # 提取第一篇文章的標題
            lines = result.split('\n')
            for line in lines:
                if line.startswith('### 1.'):
                    print(f"第一篇文章: {line[6:]}")  # 去掉 "### 1. "
                    break
            
        except Exception as e:
            print(f"錯誤: {str(e)}")
    
    print("\n" + "="*50 + "\n")


def example_crypto_news():
    """加密貨幣新聞示例"""
    print("=== 加密貨幣新聞示例 ===")
    
    crypto_queries = [
        "Bitcoin",
        "Ethereum",
        "cryptocurrency market",
        "DeFi",
        "NFT"
    ]
    
    curr_date = datetime.now().strftime("%Y-%m-%d")
    
    for query in crypto_queries:
        print(f"\n--- {query} 相關新聞 ---")
        
        try:
            result = get_newsapi_news(
                query=query,
                curr_date=curr_date,
                look_back_days=1,
                api_key=DEFAULT_CONFIG["NEWSAPI_KEY"],
                max_articles=1
            )
            
            # 提取文章數量
            if "Total:" in result:
                total_line = [line for line in result.split('\n') if 'Total:' in line][0]
                print(f"找到文章數量: {total_line.split('Total: ')[1].split(' articles')[0]}")
            else:
                print("沒有找到相關文章")
            
        except Exception as e:
            print(f"錯誤: {str(e)}")


def main():
    """主函數"""
    print("TradingAgents NewsAPI 使用示例")
    print("=" * 50)
    
    # 檢查配置
    if not DEFAULT_CONFIG.get("NEWSAPI_KEY"):
        print("❌ NewsAPI 密鑰未配置")
        print("請在 tradingagents/default_config.py 中設置 NEWSAPI_KEY")
        print("獲取密鑰: https://newsapi.org/")
        return
    
    print("✅ NewsAPI 密鑰已配置")
    print()
    
    # 運行示例
    try:
        example_basic_newsapi_usage()
        example_toolkit_usage()
        example_multiple_queries()
        example_different_sort_options()
        example_crypto_news()
        
        print("🎉 所有示例運行完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用戶中斷執行")
    except Exception as e:
        print(f"❌ 運行錯誤: {str(e)}")


if __name__ == "__main__":
    main()
