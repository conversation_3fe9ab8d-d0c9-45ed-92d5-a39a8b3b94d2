#!/usr/bin/env python3
"""
測試完整的 TradingAgentsGraph 與修復後的 Gemini API
"""

import os
import sys
sys.path.insert(0, os.path.abspath('.'))

def test_trading_graph_initialization():
    """測試 TradingAgentsGraph 初始化"""
    print("=== 測試 TradingAgentsGraph 初始化 ===")
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        from tradingagents.default_config import DEFAULT_CONFIG
        
        # 創建自定義配置
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "google"
        config["backend_url"] = "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
        config["GOOGLE_API_KEY"] = "sk-54870498"
        config["deep_think_llm"] = "gemini-1.5-flash-latest"
        config["quick_think_llm"] = "gemini-1.5-flash-latest"
        
        print(f"使用配置:")
        print(f"  LLM Provider: {config['llm_provider']}")
        print(f"  Backend URL: {config['backend_url']}")
        print(f"  Deep Think LLM: {config['deep_think_llm']}")
        print(f"  Quick Think LLM: {config['quick_think_llm']}")
        
        # 初始化 TradingAgentsGraph
        graph = TradingAgentsGraph(
            selected_analysts=["market"],  # 只選擇一個分析師來快速測試
            debug=True,
            config=config
        )
        
        print("✅ TradingAgentsGraph 初始化成功！")
        
        # 測試 LLM 調用
        print("測試 quick_thinking_llm...")
        response1 = graph.quick_thinking_llm.invoke("測試消息")
        print(f"✅ Quick thinking LLM 調用成功！回應: {response1.content[:100]}...")
        
        print("測試 deep_thinking_llm...")
        response2 = graph.deep_thinking_llm.invoke("測試消息")
        print(f"✅ Deep thinking LLM 調用成功！回應: {response2.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_propagation():
    """測試簡單的傳播"""
    print("\n=== 測試簡單傳播 ===")
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        from tradingagents.default_config import DEFAULT_CONFIG
        
        # 創建自定義配置
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "google"
        config["backend_url"] = "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com"
        config["GOOGLE_API_KEY"] = "sk-54870498"
        config["deep_think_llm"] = "gemini-1.5-flash-latest"
        config["quick_think_llm"] = "gemini-1.5-flash-latest"
        config["max_debate_rounds"] = 1  # 減少輪數以加快測試
        config["max_risk_discuss_rounds"] = 1
        config["online_tools"] = False  # 使用離線工具以加快測試
        
        # 初始化 TradingAgentsGraph
        graph = TradingAgentsGraph(
            selected_analysts=["market"],  # 只選擇一個分析師
            debug=True,
            config=config
        )
        
        print("開始簡單傳播測試...")
        print("注意：這可能需要幾分鐘時間...")
        
        # 運行簡單的傳播
        final_state, decision = graph.propagate("AAPL", "2024-01-15")
        
        print(f"✅ 傳播成功完成！")
        print(f"決策: {decision}")
        
        return True
        
    except Exception as e:
        print(f"❌ 傳播失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始測試 TradingAgentsGraph...")
    
    # 測試初始化
    init_success = test_trading_graph_initialization()
    
    if init_success:
        print("\n初始化測試成功，是否要運行傳播測試？(y/n)")
        user_input = input().strip().lower()
        
        if user_input == 'y':
            propagation_success = test_simple_propagation()
        else:
            print("跳過傳播測試")
            propagation_success = None
    else:
        print("跳過傳播測試，因為初始化失敗")
        propagation_success = False
    
    print(f"\n=== 最終測試總結 ===")
    print(f"初始化測試: {'✅' if init_success else '❌'}")
    if propagation_success is not None:
        print(f"傳播測試: {'✅' if propagation_success else '❌'}")
    else:
        print(f"傳播測試: 跳過")
    
    if init_success:
        print("\n🎉 Gemini API 配置修復成功！")
        print("您現在可以使用代理的 Gemini API 了。")
    else:
        print("\n⚠️  還有問題需要解決。")
