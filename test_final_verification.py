#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.agents.utils.memory import FinancialSituationMemory

print("=== Final Verification Test ===")
print("Testing complete TradingAgents system with Gemini API proxy")

# Test configuration - use default config and override specific values
from tradingagents.default_config import DEFAULT_CONFIG

config = DEFAULT_CONFIG.copy()
config.update({
    "llm_provider": "google",
    "backend_url": "iuiiyfzwvbzt.ap-southeast-1.clawcloudrun.com",
    "GOOGLE_API_KEY": "sk-54870498",
    "deep_think_llm": "gemini-2.0-flash-exp",
    "quick_think_llm": "gemini-2.0-flash-exp",
    "temperature": 0.7,
    "max_tokens": 1000
})

try:
    print("\n1. Testing TradingAgentsGraph initialization...")
    graph = TradingAgentsGraph(config=config)
    print("✅ TradingAgentsGraph initialized successfully")
    
    print("\n2. Testing memory system...")
    memory = FinancialSituationMemory("test_memory", config)
    print("✅ Memory system initialized successfully")
    
    # Test memory operations
    test_situations = [
        ("Market showing strong bullish signals", "Consider increasing position size"),
        ("High volatility detected", "Implement risk management strategies")
    ]
    memory.add_situations(test_situations)
    print("✅ Memory add_situations completed")
    
    memories = memory.get_memories("Market conditions are favorable", n_matches=1)
    print(f"✅ Memory retrieval completed, found {len(memories)} memories")
    
    print("\n3. Testing LLM chat functionality...")
    # Test a simple chat completion
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_google_genai import _genai_extension
    
    # Set global config for proxy
    custom_config = _genai_extension.Config(
        api_endpoint=f"https://{config['backend_url']}"
    )
    _genai_extension.set_config(custom_config)
    
    llm = ChatGoogleGenerativeAI(
        model=config["deep_think_llm"],
        google_api_key=config["GOOGLE_API_KEY"],
        transport="rest"
    )
    
    response = llm.invoke("Hello, this is a test message. Please respond briefly.")
    print(f"✅ LLM response received: {response.content[:100]}...")
    
    print("\n🎉 ALL TESTS PASSED! 🎉")
    print("✅ Gemini API proxy configuration is working correctly")
    print("✅ Memory system handles embedding failures gracefully")
    print("✅ TradingAgents system is ready for use")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
